import React from 'react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { ScoringExplanationListHeader } from './ScoringExplanationListHeader';
import { ScoringExplanationListItem } from './ScoringExplanationListItem';

export const ScoringExplanation: React.FC = () => {
  return (
    <div className="max-h-[320px] max-w-[320px] overflow-y-auto">
      <div className="px-4">
        <div className="flex py-2 justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">Score criteria</div>
        </div>
      </div>
      <Divider orientation="horizontal" />
      <ScoringExplanationListHeader title="The basics" />
      <ScoringExplanationListItem text="Description (At least 20 characters)" value="10%" />
      <ScoringExplanationListItem text="Valid assignee or responsible person" value="10%" />
      <ScoringExplanationListItem text="Last activity is within 4 weeks or if the issue is resolved." value="10%" />
      <Divider orientation="horizontal" />
      <ScoringExplanationListHeader title="Additional details" />
      <ScoringExplanationListItem text="Valid location" value="10%" />
      <ScoringExplanationListItem text="Impact is indicated" value="10%" />
      <ScoringExplanationListItem text="Type / Sub-type is indicated" value="10%" />
      <ScoringExplanationListItem text="Due date is indicated" value="5%" />
      <ScoringExplanationListItem text="Discipline" value="5%" />
      <ScoringExplanationListItem text="Includes a supporting attachment" value="20%" />
      <ScoringExplanationListItem text="Last activity is within 2 weeks or if the issue is resolved." value="10%" />
      <Divider orientation="horizontal" />
      <ScoringExplanationListHeader title="Notes" />
      <ScoringExplanationListItem text="An Issue remains visible in the heatmap for each week it is active (until resolved)." />
      <ScoringExplanationListItem text="The score can change as more details are added or if the Issue becomes stale." />
    </div>
  );
};
