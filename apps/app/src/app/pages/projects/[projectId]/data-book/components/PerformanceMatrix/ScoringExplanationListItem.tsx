export type ScoringExplanationListItemProps = {
  text: string;
  value?: string;
};
export const ScoringExplanationListItem: React.FC<ScoringExplanationListItemProps> = ({ text, value }) => {
  return (
    <div className="flex px-4 py-2 gap-2 justify-between items-center">
      <span className="text-sm leading-5 font-medium text-neutral">{text}</span>
      {value && <span className="text-sm leading-5 font-medium text-neutral-subtlest">{value}</span>}
    </div>
  );
};
